import 'package:flutter/material.dart';
import 'package:quycky/app/presenter/app_widget.dart';
import 'package:quycky/app/widgets/dialog_confirmation.dart';

class ShowDialogConfirmation {
  void Function()? onOk;
  void Function()? onClose;
  String title;
  String text;
  String buttonText;
  Widget? titleWidget;
  Widget? textWidget;
  String buttonCancelText;
  bool showCancelButton;
  bool showCloseButton;

  ShowDialogConfirmation(
      {this.onOk,
      this.onClose,
      this.title = '',
      this.text = '',
      this.buttonText = 'Ok',
      this.buttonCancelText = 'Cancel',
      this.showCancelButton = false,
      this.showCloseButton = true,
      this.titleWidget,
      this.textWidget}) {
    BuildContext context = AppWidget.globalKey.currentState!.context;
    showGeneralDialog(
      context: context,
      barrierDismissible: true,
      transitionDuration: const Duration(milliseconds: 300),
      barrierLabel: MaterialLocalizations.of(context).dialogLabel,
      barrierColor: Colors.black.withOpacity(0.5),
      pageBuilder: (context, _, __) => getConfirmationDialog(),
      transitionBuilder: getTransitionBuilder,
    );
  }

  Widget getConfirmationDialog() {
    Map<Symbol, dynamic> args = {
      #onOk: onOk,
      #onClose: onClose,
      #title: title,
      #text: text,
      #buttonText: buttonText,
      #titleWidget: titleWidget,
      #textWidget: textWidget,
      #showCloseButton: showCloseButton,
      #buttonCancelText: buttonCancelText,
      #showCancelButton: showCancelButton,
    };
    return Function.apply(DialogConfirmation.new, [], args);
  }

  Widget getTransitionBuilder(BuildContext context, Animation<double> animation,
      Animation<double> secondaryAnimation, Widget child) {
    Offset begin = const Offset(0, -1);
    begin = (const Offset(0, 1));

    return SlideTransition(
      position: CurvedAnimation(
        parent: animation,
        curve: Curves.easeOut,
      ).drive(Tween<Offset>(
        begin: begin,
        end: Offset.zero,
      )),
      child: child,
    );
  }
}
