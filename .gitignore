# Miscellaneous
*.class
*.log
*.pyc
*.swp
.vscode
.DS_Store
.atom/
.buildlog/
.history
.svn/

# IntelliJ related
*.iml
*.ipr
*.iws
.idea/

# The .vscode folder contains launch configuration and tasks you configure in
# VS Code which you may wish to be included in version control, so this line
# is commented out by default.
# .vscode/

# Flutter/Dart/Pub related
**/doc/api/
**/ios/Flutter/.last_build_id
**/ios/build
**/ios/Pods
# Firebase related
**/ios/firebase_app_id_file.json
**/ios/Runner/GoogleService-Info.plist
android/app/google-services.json
android/app/google-services-dev.json
android/app/.cxx
android/build/ios/

.dart_tool/
.flutter-plugins
.flutter-plugins-dependencies
.packages
.pub-cache/
.pub/
/build/

# Web related

# Symbolication related
app.*.symbols

# Obfuscation related
app.*.map.json

local.properties

# Android Studio will place build artifacts here
/android/app/debug
/android/app/profile
/android/app/releas

*.localinfo
*.env
certifications/*
/local

/lib/firebase_options.dart